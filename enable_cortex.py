#!/usr/bin/env python3
"""
Enable and Test Snowflake Cortex
"""

import os
import sys
import json
import getpass
from snowflake.snowpark import Session
import logging

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def get_snowflake_session():
    """Get Snowflake session with credentials"""

    # Load config
    config_path = "config/snowflake_config.json"
    if os.path.exists(config_path):
        with open(config_path, 'r') as f:
            config = json.load(f)
    else:
        config = {
            "account": "GNBXJLF-PKB97538",
            "user": "ASHUTOSH1",
            "warehouse": "SALESGENIE_AI_WH",
            "database": "SALESGENIE_AI",
            "schema": "CRM_DATA",
            "role": "SALESGENIE_AI_ROLE"
        }

    # Get password securely
    if config.get("password") == "XXXXXXXXXXXXXXXXXX" or not config.get("password"):
        password = getpass.getpass(f"Enter password for {config['user']}: ")
    else:
        password = config["password"]

    config["password"] = password

    try:
        logger.info("Creating Snowpark session...")
        session = Session.builder.configs(config).create()
        logger.info("✅ Snowpark session created successfully!")
        return session
    except Exception as e:
        logger.error(f"Failed to create Snowpark session: {str(e)}")
        return None

def enable_cortex_functions(session):
    """Enable Cortex functions in Snowflake"""
    try:
        logger.info("Testing Cortex availability...")

        # Test with different models as per documentation
        models_to_test = [
            'snowflake-arctic',
            'llama3-8b',
            'llama3-70b',
            'reka-flash',
            'mistral-large',
            'mixtral-8x7b',
            'llama2-70b-chat'
        ]

        for model in models_to_test:
            try:
                logger.info(f"Testing model: {model}")
                test_query = f"""
                SELECT SNOWFLAKE.CORTEX.COMPLETE(
                    '{model}',
                    'Hello, can you respond with just "SUCCESS"?'
                ) as response
                """

                result = session.sql(test_query).collect()

                if result and len(result) > 0:
                    response = result[0]['RESPONSE']
                    logger.info(f"✅ Model {model} is working!")
                    logger.info(f"Response: {response[:100]}...")
                    return True, model

            except Exception as model_error:
                logger.warning(f"Model {model} failed: {str(model_error)}")
                continue

        logger.error("❌ No Cortex models are available")
        return False, None

    except Exception as e:
        logger.error(f"❌ Error testing Cortex: {str(e)}")
        logger.info("This might mean:")
        logger.info("1. Cortex is not enabled in your Snowflake account")
        logger.info("2. Your role doesn't have Cortex permissions")
        logger.info("3. Cortex is not available in your region")
        logger.info("4. Account needs to be upgraded to support Cortex")
        return False, None

def test_cortex_with_crm_data(session, model='snowflake-arctic'):
    """Test Cortex with actual CRM data"""
    try:
        logger.info("Testing Cortex with CRM data...")

        # Get some sample data context
        data_query = """
        SELECT
            COUNT(*) as total_opportunities,
            SUM(amount) as total_value,
            AVG(probability) as avg_probability
        FROM opportunities
        WHERE is_won = FALSE AND is_lost = FALSE
        """

        data_result = session.sql(data_query).collect()
        data_context = data_result[0] if data_result else {}

        # Create a context-aware prompt
        prompt = f"""
        Based on this CRM data:
        - Total active opportunities: {data_context.get('TOTAL_OPPORTUNITIES', 0)}
        - Total pipeline value: ${data_context.get('TOTAL_VALUE', 0):,.2f}
        - Average probability: {data_context.get('AVG_PROBABILITY', 0):.1f}%

        Provide 3 key insights about this sales pipeline.
        """

        cortex_query = f"""
        SELECT SNOWFLAKE.CORTEX.COMPLETE(
            '{model}',
            '{prompt}'
        ) as insights
        """

        result = session.sql(cortex_query).collect()

        if result:
            logger.info("✅ Cortex analysis with CRM data successful!")
            logger.info(f"AI Insights: {result[0]['INSIGHTS']}")
            return True
        else:
            logger.error("❌ Cortex analysis failed")
            return False

    except Exception as e:
        logger.error(f"❌ Error testing Cortex with CRM data: {str(e)}")
        return False

def setup_cortex_permissions(session):
    """Setup necessary permissions for Cortex"""
    try:
        logger.info("Setting up Cortex permissions...")

        # Grant Cortex usage to current role
        session.sql("GRANT USAGE ON DATABASE SNOWFLAKE TO ROLE ACCOUNTADMIN").collect()
        session.sql("GRANT USAGE ON SCHEMA SNOWFLAKE.CORTEX TO ROLE ACCOUNTADMIN").collect()

        logger.info("✅ Cortex permissions granted")
        return True

    except Exception as e:
        logger.warning(f"⚠️ Could not grant Cortex permissions: {str(e)}")
        logger.info("This is normal if permissions are already granted or if you don't have SECURITYADMIN role")
        return False

def main():
    """Main function"""
    logger.info("🧠 Starting Snowflake Cortex setup and testing...")

    # Get Snowflake session
    session = get_snowflake_session()
    if not session:
        logger.error("❌ Could not create Snowflake session")
        return False

    try:
        # Setup permissions
        setup_cortex_permissions(session)

        # Test basic Cortex functionality
        cortex_available, working_model = enable_cortex_functions(session)
        if cortex_available:
            logger.info(f"🎉 Cortex functionality confirmed with model: {working_model}!")

            # Test with CRM data
            if test_cortex_with_crm_data(session, working_model):
                logger.info("🚀 Cortex is fully functional with your CRM data!")
                return True
            else:
                logger.warning("⚠️ Cortex works but had issues with CRM data")
                return True
        else:
            logger.error("❌ Cortex is not available or not working")
            return False

    except Exception as e:
        logger.error(f"❌ Error during Cortex setup: {str(e)}")
        return False

    finally:
        if session:
            session.close()

if __name__ == "__main__":
    success = main()
    if success:
        print("\n" + "="*60)
        print("🧠 CORTEX SETUP SUCCESSFUL!")
        print("="*60)
        print("\n✅ Snowflake Cortex is now enabled and working!")
        print("✅ You can now use AI features in your Streamlit app")
        print("\nNext step: Run 'streamlit run streamlit_app.py'")
    else:
        print("\n" + "="*60)
        print("❌ CORTEX SETUP FAILED")
        print("="*60)
        print("\n🔧 Troubleshooting steps:")
        print("1. Ensure Cortex is enabled in your Snowflake account")
        print("2. Check if your region supports Cortex")
        print("3. Verify you have the necessary permissions")
        print("4. Contact your Snowflake admin if needed")
        sys.exit(1)
