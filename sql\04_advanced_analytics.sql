/*
SalesGenie AI - Advanced Analytics & ML Models
Nihilent x Snowflake Hackathon 2025

This script creates advanced analytics views, ML models, and predictive functions
for sales forecasting, churn prediction, and intelligent recommendations.
*/

USE ROLE salesgenie_ai_role;
USE WAREHOUSE salesgenie_ai_wh;
USE DATABASE salesgenie_ai;
USE SCHEMA salesgenie_ai.analytics;

-- =====================================================
-- PREDICTIVE ANALYTICS VIEWS
-- =====================================================

-- Sales forecasting view
CREATE OR REPLACE VIEW sales_forecast AS
WITH monthly_sales AS (
    SELECT 
        DATE_TRUNC('month', actual_close_date) AS month,
        SUM(amount) AS monthly_revenue,
        COUNT(*) AS deals_closed,
        AVG(amount) AS avg_deal_size
    FROM salesgenie_ai.crm_data.opportunities
    WHERE is_won = TRUE 
    AND actual_close_date >= DATEADD('month', -12, CURRENT_DATE())
    GROUP BY DATE_TRUNC('month', actual_close_date)
),
pipeline_forecast AS (
    SELECT 
        DATE_TRUNC('month', expected_close_date) AS forecast_month,
        SUM(amount * probability / 100) AS weighted_pipeline,
        COUNT(*) AS opportunities_count,
        SUM(amount) AS total_pipeline
    FROM salesgenie_ai.crm_data.opportunities
    WHERE is_won = FALSE AND is_lost = FALSE
    AND expected_close_date >= CURRENT_DATE()
    AND expected_close_date <= DATEADD('month', 6, CURRENT_DATE())
    GROUP BY DATE_TRUNC('month', expected_close_date)
)
SELECT 
    COALESCE(ms.month, pf.forecast_month) AS period,
    COALESCE(ms.monthly_revenue, 0) AS actual_revenue,
    COALESCE(pf.weighted_pipeline, 0) AS forecasted_revenue,
    COALESCE(ms.deals_closed, 0) AS actual_deals,
    COALESCE(pf.opportunities_count, 0) AS forecasted_deals,
    COALESCE(ms.avg_deal_size, 0) AS historical_avg_deal_size,
    CASE 
        WHEN ms.month IS NOT NULL THEN 'ACTUAL'
        ELSE 'FORECAST'
    END AS data_type
FROM monthly_sales ms
FULL OUTER JOIN pipeline_forecast pf ON ms.month = pf.forecast_month
ORDER BY period;

-- Customer health score view
CREATE OR REPLACE VIEW customer_health_score AS
WITH customer_metrics AS (
    SELECT 
        c.company_id,
        c.company_name,
        c.industry,
        c.annual_revenue,
        
        -- Opportunity metrics
        COUNT(DISTINCT o.opportunity_id) AS total_opportunities,
        COUNT(DISTINCT CASE WHEN o.is_won = TRUE THEN o.opportunity_id END) AS won_opportunities,
        SUM(CASE WHEN o.is_won = TRUE THEN o.amount ELSE 0 END) AS total_revenue,
        MAX(o.actual_close_date) AS last_won_date,
        
        -- Activity metrics
        COUNT(DISTINCT a.activity_id) AS total_activities,
        MAX(a.activity_date) AS last_activity_date,
        AVG(COALESCE(SNOWFLAKE.CORTEX.SENTIMENT(a.description), 0)) AS avg_sentiment,
        
        -- Lead metrics
        COUNT(DISTINCT l.lead_id) AS total_leads,
        COUNT(DISTINCT CASE WHEN l.is_converted = TRUE THEN l.lead_id END) AS converted_leads
        
    FROM salesgenie_ai.crm_data.companies c
    LEFT JOIN salesgenie_ai.crm_data.opportunities o ON c.company_id = o.company_id
    LEFT JOIN salesgenie_ai.crm_data.activities a ON c.company_id = a.company_id
    LEFT JOIN salesgenie_ai.crm_data.leads l ON c.company_id = l.company_id
    GROUP BY c.company_id, c.company_name, c.industry, c.annual_revenue
)
SELECT 
    company_id,
    company_name,
    industry,
    annual_revenue,
    total_opportunities,
    won_opportunities,
    total_revenue,
    last_won_date,
    last_activity_date,
    avg_sentiment,
    
    -- Health score calculation (0-100)
    LEAST(100, GREATEST(0,
        -- Base score
        50 +
        -- Revenue contribution (0-25 points)
        CASE 
            WHEN total_revenue > 100000 THEN 25
            WHEN total_revenue > 50000 THEN 20
            WHEN total_revenue > 10000 THEN 15
            WHEN total_revenue > 0 THEN 10
            ELSE 0
        END +
        -- Activity recency (0-15 points)
        CASE 
            WHEN DATEDIFF('day', last_activity_date, CURRENT_DATE()) <= 7 THEN 15
            WHEN DATEDIFF('day', last_activity_date, CURRENT_DATE()) <= 30 THEN 10
            WHEN DATEDIFF('day', last_activity_date, CURRENT_DATE()) <= 90 THEN 5
            ELSE -10
        END +
        -- Sentiment score (0-10 points)
        ROUND(avg_sentiment * 10) +
        -- Opportunity success rate (0-10 points)
        CASE 
            WHEN total_opportunities > 0 THEN 
                ROUND((won_opportunities * 10.0) / total_opportunities)
            ELSE 0
        END
    )) AS health_score,
    
    -- Health classification
    CASE 
        WHEN LEAST(100, GREATEST(0,
            50 +
            CASE 
                WHEN total_revenue > 100000 THEN 25
                WHEN total_revenue > 50000 THEN 20
                WHEN total_revenue > 10000 THEN 15
                WHEN total_revenue > 0 THEN 10
                ELSE 0
            END +
            CASE 
                WHEN DATEDIFF('day', last_activity_date, CURRENT_DATE()) <= 7 THEN 15
                WHEN DATEDIFF('day', last_activity_date, CURRENT_DATE()) <= 30 THEN 10
                WHEN DATEDIFF('day', last_activity_date, CURRENT_DATE()) <= 90 THEN 5
                ELSE -10
            END +
            ROUND(avg_sentiment * 10) +
            CASE 
                WHEN total_opportunities > 0 THEN 
                    ROUND((won_opportunities * 10.0) / total_opportunities)
                ELSE 0
            END
        )) >= 80 THEN 'HEALTHY'
        WHEN LEAST(100, GREATEST(0,
            50 +
            CASE 
                WHEN total_revenue > 100000 THEN 25
                WHEN total_revenue > 50000 THEN 20
                WHEN total_revenue > 10000 THEN 15
                WHEN total_revenue > 0 THEN 10
                ELSE 0
            END +
            CASE 
                WHEN DATEDIFF('day', last_activity_date, CURRENT_DATE()) <= 7 THEN 15
                WHEN DATEDIFF('day', last_activity_date, CURRENT_DATE()) <= 30 THEN 10
                WHEN DATEDIFF('day', last_activity_date, CURRENT_DATE()) <= 90 THEN 5
                ELSE -10
            END +
            ROUND(avg_sentiment * 10) +
            CASE 
                WHEN total_opportunities > 0 THEN 
                    ROUND((won_opportunities * 10.0) / total_opportunities)
                ELSE 0
            END
        )) >= 60 THEN 'AT_RISK'
        ELSE 'UNHEALTHY'
    END AS health_status,
    
    DATEDIFF('day', last_activity_date, CURRENT_DATE()) AS days_since_last_activity

FROM customer_metrics
WHERE total_opportunities > 0 OR total_leads > 0;

-- Territory performance analysis
CREATE OR REPLACE VIEW territory_analysis AS
WITH territory_metrics AS (
    SELECT 
        u.territory,
        COUNT(DISTINCT u.user_id) AS sales_reps,
        COUNT(DISTINCT o.opportunity_id) AS total_opportunities,
        COUNT(DISTINCT CASE WHEN o.is_won = TRUE THEN o.opportunity_id END) AS won_opportunities,
        SUM(CASE WHEN o.is_won = TRUE THEN o.amount ELSE 0 END) AS total_revenue,
        COUNT(DISTINCT l.lead_id) AS total_leads,
        COUNT(DISTINCT CASE WHEN l.is_converted = TRUE THEN l.lead_id END) AS converted_leads,
        COUNT(DISTINCT c.company_id) AS unique_companies
    FROM salesgenie_ai.crm_data.sales_users u
    LEFT JOIN salesgenie_ai.crm_data.opportunities o ON u.user_id = o.sales_rep
    LEFT JOIN salesgenie_ai.crm_data.leads l ON u.user_id = l.assigned_to
    LEFT JOIN salesgenie_ai.crm_data.companies c ON (o.company_id = c.company_id OR l.company_id = c.company_id)
    WHERE u.territory IS NOT NULL
    GROUP BY u.territory
)
SELECT 
    territory,
    sales_reps,
    total_opportunities,
    won_opportunities,
    total_revenue,
    total_leads,
    converted_leads,
    unique_companies,
    
    -- Performance metrics
    ROUND(total_revenue / NULLIF(sales_reps, 0), 2) AS revenue_per_rep,
    ROUND(won_opportunities * 100.0 / NULLIF(total_opportunities, 0), 2) AS win_rate_percent,
    ROUND(converted_leads * 100.0 / NULLIF(total_leads, 0), 2) AS lead_conversion_rate,
    ROUND(total_revenue / NULLIF(won_opportunities, 0), 2) AS avg_deal_size,
    
    -- Territory ranking
    RANK() OVER (ORDER BY total_revenue DESC) AS revenue_rank,
    RANK() OVER (ORDER BY won_opportunities * 100.0 / NULLIF(total_opportunities, 0) DESC) AS win_rate_rank

FROM territory_metrics
ORDER BY total_revenue DESC;

-- =====================================================
-- MACHINE LEARNING FUNCTIONS
-- =====================================================

-- Deal closure probability prediction
CREATE OR REPLACE FUNCTION predict_deal_closure_probability(
    stage VARCHAR,
    probability FLOAT,
    amount FLOAT,
    days_in_pipeline INT,
    company_revenue FLOAT,
    industry VARCHAR,
    sales_rep_win_rate FLOAT
)
RETURNS FLOAT
LANGUAGE PYTHON
RUNTIME_VERSION = '3.9'
HANDLER = 'predict_closure'
AS
$$
def predict_closure(stage, probability, amount, days_in_pipeline, company_revenue, industry, sales_rep_win_rate):
    """
    Predict deal closure probability using ML-like algorithm
    Returns probability between 0 and 1
    """
    import math
    
    # Base probability from CRM
    base_prob = probability / 100.0 if probability else 0.5
    
    # Stage multipliers
    stage_multipliers = {
        'Discovery': 0.3,
        'Qualification': 0.5,
        'Proposal': 0.7,
        'Negotiation': 0.85,
        'Closed Won': 1.0,
        'Closed Lost': 0.0
    }
    stage_mult = stage_multipliers.get(stage, 0.5)
    
    # Amount factor (larger deals are harder to close)
    if amount > 100000:
        amount_factor = 0.9
    elif amount > 50000:
        amount_factor = 0.95
    else:
        amount_factor = 1.0
    
    # Time factor (deals get stale over time)
    if days_in_pipeline > 120:
        time_factor = 0.7
    elif days_in_pipeline > 60:
        time_factor = 0.85
    else:
        time_factor = 1.0
    
    # Company size factor
    if company_revenue and company_revenue > 10000000:
        company_factor = 1.1
    else:
        company_factor = 1.0
    
    # Industry factor
    high_conversion_industries = ['Technology', 'Financial Services']
    industry_factor = 1.1 if industry in high_conversion_industries else 1.0
    
    # Sales rep performance factor
    rep_factor = (sales_rep_win_rate / 100.0) if sales_rep_win_rate else 0.5
    rep_factor = max(0.5, min(1.5, rep_factor))  # Cap between 0.5 and 1.5
    
    # Calculate final probability
    final_prob = base_prob * stage_mult * amount_factor * time_factor * company_factor * industry_factor * rep_factor
    
    return max(0.0, min(1.0, final_prob))
$$;

-- Next best action recommendation
CREATE OR REPLACE FUNCTION recommend_next_action(
    record_type VARCHAR,  -- 'lead' or 'opportunity'
    stage VARCHAR,
    days_since_last_contact INT,
    sentiment_score FLOAT,
    amount FLOAT
)
RETURNS VARCHAR
LANGUAGE PYTHON
RUNTIME_VERSION = '3.9'
HANDLER = 'get_recommendation'
AS
$$
def get_recommendation(record_type, stage, days_since_last_contact, sentiment_score, amount):
    """
    Recommend next best action based on record state
    """
    recommendations = []
    
    if record_type == 'lead':
        if days_since_last_contact > 14:
            recommendations.append("URGENT: Follow up immediately - lead is going cold")
        elif days_since_last_contact > 7:
            recommendations.append("Schedule follow-up call within 2 days")
        
        if sentiment_score and sentiment_score < -0.3:
            recommendations.append("Address concerns - negative sentiment detected")
        elif sentiment_score and sentiment_score > 0.3:
            recommendations.append("Strike while hot - positive sentiment, push for meeting")
        
        if stage == 'New':
            recommendations.append("Qualify lead - determine budget and timeline")
        elif stage == 'Contacted':
            recommendations.append("Send relevant case studies and schedule demo")
        elif stage == 'Qualified':
            recommendations.append("Convert to opportunity and create proposal")
    
    elif record_type == 'opportunity':
        if stage == 'Discovery':
            recommendations.append("Complete needs assessment and identify decision makers")
        elif stage == 'Qualification':
            recommendations.append("Present solution demo and gather requirements")
        elif stage == 'Proposal':
            if days_since_last_contact > 7:
                recommendations.append("Follow up on proposal - check for questions")
            else:
                recommendations.append("Schedule proposal review meeting")
        elif stage == 'Negotiation':
            recommendations.append("Address objections and finalize terms")
        
        if amount and amount > 100000:
            recommendations.append("Involve sales manager for high-value deal")
        
        if sentiment_score and sentiment_score < -0.2:
            recommendations.append("Risk mitigation needed - schedule stakeholder meeting")
    
    if not recommendations:
        recommendations.append("Continue regular follow-up and relationship building")
    
    return " | ".join(recommendations[:3])  # Return top 3 recommendations
$$;

-- =====================================================
-- ADVANCED ANALYTICS VIEWS
-- =====================================================

-- Sales velocity analysis
CREATE OR REPLACE VIEW sales_velocity_analysis AS
WITH deal_velocity AS (
    SELECT 
        o.opportunity_id,
        o.opportunity_name,
        c.company_name,
        c.industry,
        o.amount,
        o.stage,
        o.sales_rep,
        o.created_date,
        o.actual_close_date,
        DATEDIFF('day', o.created_date, COALESCE(o.actual_close_date, CURRENT_DATE())) AS days_to_close,
        
        -- Calculate stage progression
        CASE o.stage
            WHEN 'Discovery' THEN 1
            WHEN 'Qualification' THEN 2
            WHEN 'Proposal' THEN 3
            WHEN 'Negotiation' THEN 4
            WHEN 'Closed Won' THEN 5
            WHEN 'Closed Lost' THEN 0
        END AS stage_number
        
    FROM salesgenie_ai.crm_data.opportunities o
    JOIN salesgenie_ai.crm_data.companies c ON o.company_id = c.company_id
    WHERE o.created_date >= DATEADD('month', -12, CURRENT_DATE())
)
SELECT 
    industry,
    stage,
    COUNT(*) AS opportunity_count,
    AVG(days_to_close) AS avg_days_to_close,
    MEDIAN(days_to_close) AS median_days_to_close,
    AVG(amount) AS avg_deal_size,
    
    -- Velocity score (higher is better)
    ROUND(AVG(amount) / NULLIF(AVG(days_to_close), 0), 2) AS velocity_score,
    
    -- Conversion rate to next stage
    COUNT(CASE WHEN stage_number >= 2 THEN 1 END) * 100.0 / COUNT(*) AS discovery_conversion_rate,
    COUNT(CASE WHEN stage_number >= 3 THEN 1 END) * 100.0 / COUNT(*) AS qualification_conversion_rate,
    COUNT(CASE WHEN stage_number >= 4 THEN 1 END) * 100.0 / COUNT(*) AS proposal_conversion_rate,
    COUNT(CASE WHEN stage_number = 5 THEN 1 END) * 100.0 / COUNT(*) AS negotiation_conversion_rate

FROM deal_velocity
GROUP BY industry, stage
ORDER BY industry, stage;

-- Competitive analysis view
CREATE OR REPLACE VIEW competitive_analysis AS
WITH competitor_data AS (
    SELECT 
        o.competitor,
        COUNT(*) AS total_deals,
        COUNT(CASE WHEN o.is_won = TRUE THEN 1 END) AS deals_won,
        COUNT(CASE WHEN o.is_lost = TRUE THEN 1 END) AS deals_lost,
        SUM(CASE WHEN o.is_won = TRUE THEN o.amount ELSE 0 END) AS revenue_won,
        SUM(CASE WHEN o.is_lost = TRUE THEN o.amount ELSE 0 END) AS revenue_lost,
        AVG(o.amount) AS avg_deal_size,
        AVG(DATEDIFF('day', o.created_date, COALESCE(o.actual_close_date, CURRENT_DATE()))) AS avg_sales_cycle
    FROM salesgenie_ai.crm_data.opportunities o
    WHERE o.competitor IS NOT NULL
    AND o.created_date >= DATEADD('month', -12, CURRENT_DATE())
    GROUP BY o.competitor
)
SELECT 
    competitor,
    total_deals,
    deals_won,
    deals_lost,
    revenue_won,
    revenue_lost,
    avg_deal_size,
    avg_sales_cycle,
    
    -- Win rate against this competitor
    ROUND(deals_won * 100.0 / NULLIF(total_deals, 0), 2) AS win_rate_percent,
    
    -- Threat level
    CASE 
        WHEN deals_won * 100.0 / NULLIF(total_deals, 0) < 30 THEN 'HIGH_THREAT'
        WHEN deals_won * 100.0 / NULLIF(total_deals, 0) < 50 THEN 'MEDIUM_THREAT'
        ELSE 'LOW_THREAT'
    END AS threat_level,
    
    -- Market share (by deal count)
    ROUND(total_deals * 100.0 / SUM(total_deals) OVER (), 2) AS market_share_percent

FROM competitor_data
ORDER BY total_deals DESC;

COMMIT;
