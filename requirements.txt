# SalesGenie AI - Python Dependencies
# Nihilent x Snowflake Hackathon 2025

# Core Snowflake packages
snowflake-connector-python>=3.7.0
snowflake-snowpark-python>=1.11.0
snowflake-ml-python>=1.1.2

# Streamlit and web framework
streamlit>=1.28.0
streamlit-chat>=0.1.1
streamlit-option-menu>=0.3.6

# Data processing and analytics
pandas>=2.0.0
numpy>=1.24.0
plotly>=5.17.0
altair>=5.0.0

# AI and ML libraries
scikit-learn>=1.3.0
transformers>=4.30.0
torch>=2.0.0

# API and web requests
requests>=2.31.0
httpx>=0.24.0

# Configuration and utilities
pyyaml>=6.0
python-dotenv>=1.0.0
click>=8.1.0
rich>=13.0.0

# OpenTelemetry for Snowflake connector
opentelemetry-api>=1.20.0
opentelemetry-sdk>=1.20.0
opentelemetry-instrumentation>=0.41b0

# Development and testing
pytest>=7.4.0
pytest-cov>=4.1.0
black>=23.0.0
flake8>=6.0.0
mypy>=1.5.0

# Documentation
mkdocs>=1.5.0
mkdocs-material>=9.0.0

# Optional: Voice input support
speech-recognition>=3.10.0
pyaudio>=0.2.11

# Optional: Advanced NLP
spacy>=3.6.0
nltk>=3.8.0
