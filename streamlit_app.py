"""
SalesGenie AI - Conversational AI for Sales Excellence
Nihilent x Snowflake Hackathon 2025

Main Streamlit application providing conversational interface to sales CRM data
using Snowflake Cortex AI services.
"""

import streamlit as st

# Page configuration - MUST be first Streamlit command
st.set_page_config(
    page_title="SalesGenie AI",
    page_icon="🚀",
    layout="wide",
    initial_sidebar_state="expanded"
)

import json
import pandas as pd
from typing import List, Dict, Optional, Tuple
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime, timedelta
import os
import sys

# Snowflake imports with error handling
try:
    import snowflake.connector
    from snowflake.snowpark import Session
    # Note: snowflake.cortex might not be available in all versions
    try:
        from snowflake.cortex import Complete, Sentiment, Summarize, ExtractAnswer
        CORTEX_AVAILABLE = True
    except ImportError:
        CORTEX_AVAILABLE = False
    SNOWFLAKE_AVAILABLE = True
except ImportError as e:
    SNOWFLAKE_AVAILABLE = False
    CORTEX_AVAILABLE = False

import requests

# Custom CSS for better UI
st.markdown("""
<style>
    .main-header {
        font-size: 3rem;
        font-weight: bold;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .metric-card {
        background-color: #f0f2f6;
        padding: 1rem;
        border-radius: 0.5rem;
        border-left: 4px solid #1f77b4;
    }
    .chat-message {
        padding: 1rem;
        border-radius: 0.5rem;
        margin: 0.5rem 0;
    }
    .user-message {
        background-color: #e3f2fd;
        border-left: 4px solid #2196f3;
    }
    .ai-message {
        background-color: #f3e5f5;
        border-left: 4px solid #9c27b0;
    }
    .sidebar .sidebar-content {
        background-color: #f8f9fa;
    }
</style>
""", unsafe_allow_html=True)

# Constants
API_ENDPOINT = "/api/v2/cortex/analyst/message"
API_TIMEOUT = 30000  # 30 seconds
SEMANTIC_MODEL_PATH = "salesgenie_ai.ai_services.sales_crm_semantic_model"

# Initialize session state
if "messages" not in st.session_state:
    st.session_state.messages = []
if "snowflake_session" not in st.session_state:
    st.session_state.snowflake_session = None

def init_snowflake_connection():
    """Initialize Snowflake connection"""
    if not SNOWFLAKE_AVAILABLE:
        st.warning("Snowflake packages not available. Using demo mode.")
        return None

    if not CORTEX_AVAILABLE:
        st.info("ℹ️ Snowflake Cortex not available. Using standard Snowflake connection with demo AI responses.")

    try:
        # Try to load from config file
        config_path = "config/snowflake_config.json"
        if os.path.exists(config_path):
            with open(config_path, 'r') as f:
                config = json.load(f)
            connection_params = {
                "account": config.get("account", "GNBXJLF-PKB97538"),
                "user": config.get("user", "ASHUTOSH1"),
                "password": config.get("password", "your_password"),
                "warehouse": config.get("warehouse", "salesgenie_ai_wh"),
                "database": config.get("database", "salesgenie_ai"),
                "schema": config.get("schema", "crm_data"),
                "role": config.get("role", "ACCOUNTADMIN")
            }
        else:
            # Fallback to environment variables or default values
            connection_params = {
                "account": os.getenv("SNOWFLAKE_ACCOUNT", "GNBXJLF-PKB97538"),
                "user": os.getenv("SNOWFLAKE_USER", "ASHUTOSH1"),
                "password": os.getenv("SNOWFLAKE_PASSWORD", "your_password"),
                "warehouse": os.getenv("SNOWFLAKE_WAREHOUSE", "salesgenie_ai_wh"),
                "database": os.getenv("SNOWFLAKE_DATABASE", "salesgenie_ai"),
                "schema": os.getenv("SNOWFLAKE_SCHEMA", "crm_data"),
                "role": os.getenv("SNOWFLAKE_ROLE", "ACCOUNTADMIN")
            }

        # Check if we have valid credentials
        if connection_params["password"] == "your_password" or connection_params["password"] == "XXXXXXXXXXXXXXXXXX":
            st.warning("⚠️ Snowflake password not configured. Using demo mode with sample data.")
            return None

        # Use regular snowflake connector instead of snowpark
        connection = snowflake.connector.connect(**connection_params)
        st.session_state.snowflake_session = connection
        st.success("✅ Connected to Snowflake successfully!")
        return connection

    except Exception as e:
        st.error(f"❌ Failed to connect to Snowflake: {str(e)}")
        st.info("💡 Using demo mode with sample data. Configure Snowflake credentials for live data.")
        return None

def get_real_data_from_snowflake(query_type: str) -> Dict:
    """Get real data from Snowflake based on query type"""
    session = st.session_state.snowflake_session
    if not session:
        return {}

    try:
        if query_type == "pipeline":
            df = session.sql("""
                SELECT stage, COUNT(*) as count, SUM(amount) as total_value, AVG(amount) as avg_value
                FROM salesgenie_ai.crm_data.opportunities
                WHERE is_won = FALSE AND is_lost = FALSE
                GROUP BY stage
                ORDER BY total_value DESC
            """).to_pandas()
            return {"pipeline_data": df.to_dict('records')}

        elif query_type == "leads":
            df = session.sql("""
                SELECT lead_source, COUNT(*) as total_leads,
                       AVG(lead_score) as avg_score,
                       COUNT(CASE WHEN is_converted = TRUE THEN 1 END) as converted
                FROM salesgenie_ai.crm_data.leads
                GROUP BY lead_source
                ORDER BY avg_score DESC
            """).to_pandas()
            return {"lead_data": df.to_dict('records')}

        elif query_type == "performance":
            df = session.sql("""
                SELECT u.first_name || ' ' || u.last_name as sales_rep,
                       COUNT(o.opportunity_id) as total_opps,
                       COUNT(CASE WHEN o.is_won = TRUE THEN 1 END) as won_opps,
                       SUM(CASE WHEN o.is_won = TRUE THEN o.amount ELSE 0 END) as revenue
                FROM salesgenie_ai.crm_data.sales_users u
                LEFT JOIN salesgenie_ai.crm_data.opportunities o ON u.user_id = o.sales_rep
                WHERE u.role = 'sales_rep'
                GROUP BY u.first_name, u.last_name
                ORDER BY revenue DESC
            """).to_pandas()
            return {"performance_data": df.to_dict('records')}

    except Exception as e:
        st.error(f"Error querying Snowflake: {str(e)}")
        return {}

    return {}

def get_analyst_response(messages: List[Dict]) -> Tuple[Dict, Optional[str]]:
    """
    Send chat history to the Cortex Analyst API and return the response.

    Args:
        messages (List[Dict]): The conversation history.

    Returns:
        Tuple[Dict, Optional[str]]: The response and any error message.
    """
    try:
        if messages and len(messages) > 0:
            user_message = messages[-1]["content"]

            # Try to get real data if Snowflake is connected
            if st.session_state.snowflake_session:
                # Use real Snowflake data
                if "pipeline" in user_message.lower():
                    real_data = get_real_data_from_snowflake("pipeline")
                    if real_data:
                        pipeline_data = real_data.get("pipeline_data", [])
                        total_value = sum(item.get("TOTAL_VALUE", 0) for item in pipeline_data)
                        total_count = sum(item.get("COUNT", 0) for item in pipeline_data)

                        content = f"Based on your current sales pipeline:\n\n**Pipeline Summary:**\n- Total Pipeline Value: ${total_value:,.0f}\n- Number of Active Opportunities: {total_count}\n\n**By Stage:**\n"
                        for item in pipeline_data:
                            stage = item.get("STAGE", "Unknown")
                            value = item.get("TOTAL_VALUE", 0)
                            count = item.get("COUNT", 0)
                            content += f"- {stage}: ${value:,.0f} ({count} opportunities)\n"

                        return {
                            "message": {
                                "role": "analyst",
                                "content": content
                            },
                            "request_id": "real_data_pipeline"
                        }, None

                elif "lead" in user_message.lower():
                    real_data = get_real_data_from_snowflake("leads")
                    if real_data:
                        lead_data = real_data.get("lead_data", [])
                        content = "Here's your lead analysis:\n\n**Lead Performance by Source:**\n"
                        for item in lead_data:
                            source = item.get("LEAD_SOURCE", "Unknown")
                            total = item.get("TOTAL_LEADS", 0)
                            score = item.get("AVG_SCORE", 0)
                            converted = item.get("CONVERTED", 0)
                            conversion_rate = (converted / total * 100) if total > 0 else 0
                            content += f"- {source}: {total} leads (avg score: {score:.1f}, conversion: {conversion_rate:.1f}%)\n"

                        return {
                            "message": {
                                "role": "analyst",
                                "content": content
                            },
                            "request_id": "real_data_leads"
                        }, None

                elif "performance" in user_message.lower() or "rep" in user_message.lower():
                    real_data = get_real_data_from_snowflake("performance")
                    if real_data:
                        perf_data = real_data.get("performance_data", [])
                        content = "**Sales Rep Performance Analysis:**\n\n"
                        for item in perf_data:
                            rep = item.get("SALES_REP", "Unknown")
                            total_opps = item.get("TOTAL_OPPS", 0)
                            won_opps = item.get("WON_OPPS", 0)
                            revenue = item.get("REVENUE", 0)
                            win_rate = (won_opps / total_opps * 100) if total_opps > 0 else 0
                            content += f"**{rep}:**\n- Opportunities: {total_opps}\n- Won: {won_opps}\n- Revenue: ${revenue:,.0f}\n- Win Rate: {win_rate:.1f}%\n\n"

                        return {
                            "message": {
                                "role": "analyst",
                                "content": content
                            },
                            "request_id": "real_data_performance"
                        }, None

            # Fallback to demo responses if no Snowflake connection
            if "pipeline" in user_message.lower():
                response = {
                    "message": {
                        "role": "analyst",
                        "content": "Based on your current sales pipeline, here's what I found:\n\n**Pipeline Summary:**\n- Total Pipeline Value: $535,000\n- Number of Active Opportunities: 6\n- Average Deal Size: $89,167\n\n**By Stage:**\n- Discovery: $165,000 (2 opportunities)\n- Qualification: $150,000 (1 opportunity) \n- Proposal: $160,000 (2 opportunities)\n- Negotiation: $95,000 (2 opportunities)\n\n**Key Insights:**\n- 75% of pipeline value is in early stages (Discovery/Qualification)\n- Negotiation stage deals have highest close probability\n- Recommend focusing on moving Proposal stage deals forward"
                    },
                    "request_id": "demo_request_123"
                }
            elif "lead" in user_message.lower():
                response = {
                    "message": {
                        "role": "analyst",
                        "content": "Here's your lead analysis:\n\n**Lead Performance:**\n- Total Active Leads: 6\n- Average Lead Score: 69.2\n- Conversion Rate: 16.7%\n\n**By Source:**\n- Website: 1 lead (85.5 score) - Hot\n- Trade Show: 1 lead (65.0 score) - Warm\n- Referral: 1 lead (90.2 score) - Hot\n- Cold Call: 1 lead (45.0 score) - Cold\n- LinkedIn: 1 lead (75.8 score) - Warm\n- Email Campaign: 1 lead (55.0 score) - Warm\n\n**Recommendations:**\n- Prioritize referral and website leads (highest scores)\n- Follow up on overdue LinkedIn lead\n- Nurture cold call lead with targeted content"
                    },
                    "request_id": "demo_request_124"
                }
            elif "performance" in user_message.lower() or "rep" in user_message.lower():
                response = {
                    "message": {
                        "role": "analyst",
                        "content": "**Sales Rep Performance Analysis:**\n\n**Sarah Johnson (West Coast):**\n- Opportunities: 3\n- Pipeline Value: $170,000\n- Win Rate: 33.3%\n- Revenue: $95,000\n\n**Mike Davis (East Coast):**\n- Opportunities: 3  \n- Pipeline Value: $225,000\n- Win Rate: 0%\n- Revenue: $0\n\n**Lisa Wilson (Central):**\n- Opportunities: 2\n- Pipeline Value: $170,000\n- Win Rate: 0%\n- Revenue: $0\n\n**Key Insights:**\n- Sarah leads in closed revenue\n- Mike has highest pipeline value but needs closing support\n- Lisa needs lead generation assistance\n- Overall team win rate: 12.5%"
                    },
                    "request_id": "demo_request_125"
                }
            else:
                response = {
                    "message": {
                        "role": "analyst",
                        "content": f"I can help you analyze your sales data! Here are some things you can ask me:\n\n**Pipeline Analysis:**\n- 'Show me the pipeline by stage'\n- 'What are my top opportunities?'\n- 'Which deals are at risk?'\n\n**Lead Management:**\n- 'How are my leads performing?'\n- 'Which leads need follow-up?'\n- 'What's my conversion rate by source?'\n\n**Sales Performance:**\n- 'Show me sales rep performance'\n- 'What's our team win rate?'\n- 'Who needs coaching support?'\n\n**Forecasting:**\n- 'What's our quarterly forecast?'\n- 'Which deals will close this month?'\n- 'Show me revenue trends'\n\nWhat would you like to explore?"
                    },
                    "request_id": "demo_request_126"
                }

            return response, None

    except Exception as e:
        error_msg = f"Error calling Cortex Analyst: {str(e)}"
        return {}, error_msg

def create_crm_action(action_type: str, details: Dict) -> str:
    """
    Process CRM actions like creating leads, updating opportunities, etc.

    Args:
        action_type (str): Type of CRM action
        details (Dict): Action details

    Returns:
        str: Success message
    """
    if action_type == "create_lead":
        return f"✅ Created new lead for {details.get('company', 'Unknown Company')}"
    elif action_type == "update_opportunity":
        return f"✅ Updated opportunity {details.get('name', 'Unknown')} to {details.get('stage', 'Unknown')} stage"
    elif action_type == "schedule_follow_up":
        return f"✅ Scheduled follow-up with {details.get('contact', 'Unknown')} for {details.get('date', 'Unknown')}"
    else:
        return "✅ CRM action completed successfully"

def display_sales_metrics():
    """Display key sales metrics in the sidebar"""
    st.sidebar.markdown("### 📊 Key Metrics")

    # Sample metrics - in real app, these would come from Snowflake
    col1, col2 = st.sidebar.columns(2)

    with col1:
        st.metric("Pipeline Value", "$535K", "+12%")
        st.metric("Active Leads", "6", "+2")

    with col2:
        st.metric("Win Rate", "12.5%", "-2.1%")
        st.metric("Avg Deal Size", "$89K", "+5%")

def display_quick_actions():
    """Display quick action buttons"""
    st.sidebar.markdown("### ⚡ Quick Actions")

    if st.sidebar.button("📈 Pipeline Review"):
        st.session_state.messages.append({
            "role": "user",
            "content": "Show me my current sales pipeline by stage"
        })
        st.rerun()

    if st.sidebar.button("🎯 Lead Analysis"):
        st.session_state.messages.append({
            "role": "user",
            "content": "Analyze my lead performance and conversion rates"
        })
        st.rerun()

    if st.sidebar.button("👥 Team Performance"):
        st.session_state.messages.append({
            "role": "user",
            "content": "Show me sales rep performance metrics"
        })
        st.rerun()

    if st.sidebar.button("📅 Follow-up Alerts"):
        st.session_state.messages.append({
            "role": "user",
            "content": "Which leads and opportunities need follow-up?"
        })
        st.rerun()

def main():
    """Main application function"""

    # Initialize Snowflake connection if not already done
    if st.session_state.snowflake_session is None and SNOWFLAKE_AVAILABLE:
        with st.spinner("Connecting to Snowflake..."):
            init_snowflake_connection()

    # Header
    st.markdown('<h1 class="main-header">🚀 SalesGenie AI</h1>', unsafe_allow_html=True)
    st.markdown('<p style="text-align: center; font-size: 1.2rem; color: #666;">Conversational AI for Sales Excellence</p>', unsafe_allow_html=True)

    # Connection status indicator
    if st.session_state.snowflake_session:
        st.success("🔗 Connected to Snowflake - Using live data")
    else:
        st.info("📊 Demo mode - Using sample data")

    # Sidebar
    with st.sidebar:
        st.markdown("### 🎯 SalesGenie AI")
        st.markdown("Your intelligent sales assistant powered by Snowflake Cortex")

        display_sales_metrics()
        display_quick_actions()

        st.markdown("---")
        st.markdown("### 🔧 Settings")

        # Model selection
        model_option = st.selectbox(
            "AI Model",
            ["claude-3-5-sonnet", "llama3.1-70b", "mistral-large2"],
            help="Choose the AI model for responses"
        )

        # Voice input toggle
        voice_enabled = st.checkbox("🎤 Voice Input", help="Enable voice-to-text input")

        if st.button("🗑️ Clear Chat"):
            st.session_state.messages = []
            st.rerun()

    # Main chat interface
    st.markdown("### 💬 Chat with your Sales Data")

    # Display chat messages
    for message in st.session_state.messages:
        if message["role"] == "user":
            st.markdown(f'<div class="chat-message user-message"><strong>You:</strong> {message["content"]}</div>', unsafe_allow_html=True)
        else:
            st.markdown(f'<div class="chat-message ai-message"><strong>SalesGenie AI:</strong> {message["content"]}</div>', unsafe_allow_html=True)

    # Chat input
    user_input = st.chat_input("Ask me about your sales data, pipeline, leads, or team performance...")

    if user_input:
        # Add user message
        st.session_state.messages.append({"role": "user", "content": user_input})

        # Get AI response
        with st.spinner("Analyzing your sales data..."):
            response, error = get_analyst_response(st.session_state.messages)

            if error:
                st.error(error)
            else:
                ai_response = response.get("message", {}).get("content", "I'm sorry, I couldn't process that request.")
                st.session_state.messages.append({"role": "assistant", "content": ai_response})

        st.rerun()

    # Sample questions
    if not st.session_state.messages:
        st.markdown("### 💡 Try asking me:")

        col1, col2, col3 = st.columns(3)

        with col1:
            if st.button("📊 Pipeline Analysis"):
                st.session_state.messages.append({
                    "role": "user",
                    "content": "Show me my sales pipeline breakdown by stage"
                })
                st.rerun()

        with col2:
            if st.button("🎯 Lead Insights"):
                st.session_state.messages.append({
                    "role": "user",
                    "content": "Analyze my lead performance and scoring"
                })
                st.rerun()

        with col3:
            if st.button("👥 Team Metrics"):
                st.session_state.messages.append({
                    "role": "user",
                    "content": "Show me sales team performance"
                })
                st.rerun()

if __name__ == "__main__":
    main()
