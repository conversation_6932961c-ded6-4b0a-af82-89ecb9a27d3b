#!/usr/bin/env python3
"""
SalesGenie AI - Deployment Script
Nihilent x Snowflake Hackathon 2025

This script deploys the complete SalesGenie AI solution to Snowflake,
including database setup, sample data, AI services, and Streamlit app.
"""

import os
import sys
import json
import time
from pathlib import Path
import snowflake.connector
from snowflake.connector import DictCursor
import argparse
import logging

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class SalesGenieDeployer:
    """Main deployment class for SalesGenie AI"""
    
    def __init__(self, config_file: str = None):
        """Initialize deployer with configuration"""
        self.config = self.load_config(config_file)
        self.connection = None
        
    def load_config(self, config_file: str = None) -> dict:
        """Load Snowflake connection configuration"""
        if config_file and os.path.exists(config_file):
            with open(config_file, 'r') as f:
                return json.load(f)
        
        # Default configuration - should be customized
        return {
            "account": os.getenv("SNOWFLAKE_ACCOUNT", "your_account"),
            "user": os.getenv("SNOWFLAKE_USER", "your_user"),
            "password": os.getenv("SNOWFLAKE_PASSWORD", "your_password"),
            "warehouse": "SALESGENIE_AI_WH",
            "database": "SALESGENIE_AI",
            "schema": "CRM_DATA",
            "role": "ACCOUNTADMIN"
        }
        # return {
        #     "account":  "GNBXJLF-PKB97538",
        #     "user": "ASHUTOSH1",
        #     "password": "**************",
        #     "warehouse": "SALESGENIE_AI_WH",
        #     "database": "SALESGENIE_AI",
        #     "schema": "CRM_DATA",
        #     "role": "ACCOUNTADMIN"
        # }
    
    
    def connect_to_snowflake(self):
        """Establish connection to Snowflake"""
        try:
            logger.info("Connecting to Snowflake...")
            self.connection = snowflake.connector.connect(
                account=self.config["account"],
                user=self.config["user"],
                password=self.config["password"],
                warehouse=self.config["warehouse"],
                database=self.config["database"],
                schema=self.config["schema"],
                role=self.config["role"]
            )
            logger.info("Successfully connected to Snowflake")
            return True
        except Exception as e:
            logger.error(f"Failed to connect to Snowflake: {str(e)}")
            return False
    
    def execute_sql_file(self, file_path: str) -> bool:
        """Execute SQL commands from a file"""
        try:
            logger.info(f"Executing SQL file: {file_path}")
            
            with open(file_path, 'r') as f:
                sql_content = f.read()
            
            # Split by semicolon and execute each statement
            statements = [stmt.strip() for stmt in sql_content.split(';') if stmt.strip()]
            
            cursor = self.connection.cursor()
            
            for i, statement in enumerate(statements):
                if statement.upper().startswith(('--', '/*')):
                    continue
                
                try:
                    logger.debug(f"Executing statement {i+1}/{len(statements)}")
                    cursor.execute(statement)
                    logger.debug(f"Statement executed successfully")
                except Exception as e:
                    logger.warning(f"Statement {i+1} failed: {str(e)}")
                    # Continue with next statement
            
            cursor.close()
            logger.info(f"Successfully executed SQL file: {file_path}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to execute SQL file {file_path}: {str(e)}")
            return False
    
    def upload_semantic_model(self) -> bool:
        """Upload semantic model to Snowflake stage"""
        try:
            logger.info("Uploading semantic model...")
            
            # Read semantic model file
            semantic_model_path = "semantic_models/sales_crm_semantic_model.yaml"
            if not os.path.exists(semantic_model_path):
                logger.error(f"Semantic model file not found: {semantic_model_path}")
                return False
            
            cursor = self.connection.cursor()
            
            # Create stage if not exists
            cursor.execute("""
                CREATE STAGE IF NOT EXISTS salesgenie_ai.ai_services.semantic_models
                DIRECTORY = (ENABLE = TRUE)
                COMMENT = 'Stage for Cortex Analyst semantic models'
            """)
            
            # Upload file to stage
            cursor.execute(f"""
                PUT file://{os.path.abspath(semantic_model_path)} 
                @salesgenie_ai.ai_services.semantic_models
                AUTO_COMPRESS = FALSE
                OVERWRITE = TRUE
            """)
            
            cursor.close()
            logger.info("Successfully uploaded semantic model")
            return True
            
        except Exception as e:
            logger.error(f"Failed to upload semantic model: {str(e)}")
            return False
    
    def create_streamlit_app(self) -> bool:
        """Create Streamlit app in Snowflake"""
        try:
            logger.info("Creating Streamlit app...")
            
            # Read Streamlit app code
            app_path = "streamlit_app.py"
            if not os.path.exists(app_path):
                logger.error(f"Streamlit app file not found: {app_path}")
                return False
            
            with open(app_path, 'r') as f:
                app_code = f.read()
            
            cursor = self.connection.cursor()
            
            # Create Streamlit app
            cursor.execute(f"""
                CREATE OR REPLACE STREAMLIT salesgenie_ai.ai_services.salesgenie_ai_app
                ROOT_LOCATION = '@salesgenie_ai.ai_services.streamlit_stage'
                MAIN_FILE = 'streamlit_app.py'
                QUERY_WAREHOUSE = 'salesgenie_ai_wh'
                COMMENT = 'SalesGenie AI - Conversational AI for Sales Excellence'
            """)
            
            cursor.close()
            logger.info("Successfully created Streamlit app")
            return True
            
        except Exception as e:
            logger.error(f"Failed to create Streamlit app: {str(e)}")
            return False
    
    def verify_deployment(self) -> bool:
        """Verify that deployment was successful"""
        try:
            logger.info("Verifying deployment...")
            
            cursor = self.connection.cursor(DictCursor)
            
            # Check tables
            cursor.execute("SHOW TABLES IN salesgenie_ai.crm_data")
            tables = cursor.fetchall()
            expected_tables = ['companies', 'contacts', 'leads', 'opportunities', 'products', 'activities', 'sales_users']
            
            table_names = [table['name'].lower() for table in tables]
            missing_tables = [t for t in expected_tables if t not in table_names]
            
            if missing_tables:
                logger.error(f"Missing tables: {missing_tables}")
                return False
            
            # Check data
            cursor.execute("SELECT COUNT(*) as count FROM salesgenie_ai.crm_data.companies")
            company_count = cursor.fetchone()['COUNT']
            
            cursor.execute("SELECT COUNT(*) as count FROM salesgenie_ai.crm_data.opportunities")
            opp_count = cursor.fetchone()['COUNT']
            
            if company_count == 0 or opp_count == 0:
                logger.error("No sample data found")
                return False
            
            # Check views
            cursor.execute("SHOW VIEWS IN salesgenie_ai.analytics")
            views = cursor.fetchall()
            
            if len(views) == 0:
                logger.error("No analytics views found")
                return False
            
            cursor.close()
            logger.info(f"Deployment verified successfully!")
            logger.info(f"- Companies: {company_count}")
            logger.info(f"- Opportunities: {opp_count}")
            logger.info(f"- Analytics views: {len(views)}")
            
            return True
            
        except Exception as e:
            logger.error(f"Deployment verification failed: {str(e)}")
            return False
    
    def deploy(self) -> bool:
        """Execute complete deployment"""
        logger.info("Starting SalesGenie AI deployment...")
        
        # Connect to Snowflake
        if not self.connect_to_snowflake():
            return False
        
        try:
            # Step 1: Database setup
            if not self.execute_sql_file("sql/01_setup_database.sql"):
                logger.error("Database setup failed")
                return False
            
            # Step 2: Sample data
            if not self.execute_sql_file("sql/02_sample_data.sql"):
                logger.error("Sample data loading failed")
                return False
            
            # Step 3: AI services setup
            if not self.execute_sql_file("sql/03_ai_services_setup.sql"):
                logger.error("AI services setup failed")
                return False
            
            # Step 4: Advanced analytics
            if not self.execute_sql_file("sql/04_advanced_analytics.sql"):
                logger.error("Advanced analytics setup failed")
                return False
            
            # Step 5: Upload semantic model
            if not self.upload_semantic_model():
                logger.error("Semantic model upload failed")
                return False
            
            # Step 6: Verify deployment
            if not self.verify_deployment():
                logger.error("Deployment verification failed")
                return False
            
            logger.info("🎉 SalesGenie AI deployment completed successfully!")
            logger.info("You can now access the Streamlit app in your Snowflake account")
            
            return True
            
        except Exception as e:
            logger.error(f"Deployment failed: {str(e)}")
            return False
        
        finally:
            if self.connection:
                self.connection.close()

def main():
    """Main function"""
    parser = argparse.ArgumentParser(description="Deploy SalesGenie AI to Snowflake")
    parser.add_argument("--config", help="Configuration file path")
    parser.add_argument("--verbose", "-v", action="store_true", help="Verbose logging")
    
    args = parser.parse_args()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # Check if we're in the right directory
    if not os.path.exists("sql/01_setup_database.sql"):
        logger.error("Please run this script from the project root directory")
        sys.exit(1)
    
    # Deploy
    deployer = SalesGenieDeployer(args.config)
    success = deployer.deploy()
    
    if success:
        print("\n" + "="*60)
        print("🚀 SALESGENIE AI DEPLOYMENT SUCCESSFUL! 🚀")
        print("="*60)
        print("\nNext steps:")
        print("1. Access your Snowflake account")
        print("2. Navigate to Streamlit apps")
        print("3. Find 'SalesGenie AI' app")
        print("4. Start asking questions about your sales data!")
        print("\nSample questions to try:")
        print("- 'Show me my sales pipeline by stage'")
        print("- 'Which leads need follow-up?'")
        print("- 'How is my sales team performing?'")
        print("- 'What's my quarterly forecast?'")
        sys.exit(0)
    else:
        print("\n" + "="*60)
        print("❌ DEPLOYMENT FAILED")
        print("="*60)
        print("\nPlease check the logs above for error details.")
        print("Common issues:")
        print("- Incorrect Snowflake credentials")
        print("- Insufficient privileges")
        print("- Network connectivity issues")
        sys.exit(1)

if __name__ == "__main__":
    main()
